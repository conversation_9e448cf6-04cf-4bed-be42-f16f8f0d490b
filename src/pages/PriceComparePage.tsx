import React, { useState } from 'react';
import { Search, Filter, TrendingDown, DollarSign, Star, Shield } from 'lucide-react';
import PriceComparisonSystem from '../components/PriceComparisonSystem';

const PriceComparePage: React.FC = () => {
  const [selectedProduct, setSelectedProduct] = useState<string>('nrt-001');
  const [selectedProductName, setSelectedProductName] = useState<string>('Nicorette Gum 2mg');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  return (
    <div className="min-h-screen bg-background">
      {/* Apple Mac Desktop Premium Header Section */}
      <div className="bg-background/99 backdrop-blur-2xl border-b border-border/25 shadow-[0_2px_8px_rgba(0,0,0,0.02)]">
        <div className="max-w-7xl mx-auto px-8 py-16">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-4 bg-primary/8 backdrop-blur-xl px-8 py-4 rounded-2xl mb-8 border border-primary/10">
              <TrendingDown className="w-6 h-6 text-primary" />
              <span className="text-primary font-refined font-semibold text-lg">Price Comparison</span>
            </div>
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-royal text-foreground mb-8 tracking-royal leading-[1.02]">NRT Price Comparison</h1>
            <p className="text-xl sm:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-sophisticated">
              Compare prices across stores and online vendors to find the best deals on NRT products.
              Save money on your quit smoking journey with real-time price comparisons.
            </p>
            <p className="text-base text-muted-foreground mt-4 font-refined">
              Find the lowest prices from verified stores and vendors with affiliate partnerships.
            </p>
          </div>

          {/* Apple Mac Desktop Premium Product Selection */}
          <div className="max-w-4xl mx-auto mb-12">
            <div className="relative group">
              <Search className="absolute left-8 top-1/2 transform -translate-y-1/2 text-muted-foreground/50 w-7 h-7 group-focus-within:text-primary transition-colors duration-500" />
              <input
                type="text"
                placeholder="Search products to compare prices..."
                className="w-full pl-18 pr-12 py-7 border border-border/25 rounded-[2rem] bg-background/99 backdrop-blur-2xl focus:outline-none focus:ring-3 focus:ring-primary/25 focus:border-primary/35 transition-all duration-500 text-xl font-sophisticated placeholder-muted-foreground/40 shadow-[0_6px_20px_rgba(0,0,0,0.04)] focus:shadow-[0_8px_28px_rgba(0,0,0,0.06)] hover:border-primary/20 focus:scale-[1.01]"
                value={selectedProductName}
                onChange={(e) => setSelectedProductName(e.target.value)}
              />
            </div>
          </div>

          {/* Apple Mac Desktop Premium Statistics Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-6xl mx-auto">
            <div className="bg-card/80 backdrop-blur-xl rounded-3xl p-8 text-center border border-border/20 shadow-[0_6px_20px_rgba(0,0,0,0.04)] hover:shadow-[0_8px_28px_rgba(0,0,0,0.06)] transition-all duration-400 hover:scale-[1.02]">
              <DollarSign className="w-8 h-8 text-primary mx-auto mb-4" />
              <div className="text-2xl font-royal font-semibold text-foreground mb-2">$15.99+</div>
              <div className="text-base text-muted-foreground font-refined">Lowest Price</div>
            </div>
            <div className="bg-card/80 backdrop-blur-xl rounded-3xl p-8 text-center border border-border/20 shadow-[0_6px_20px_rgba(0,0,0,0.04)] hover:shadow-[0_8px_28px_rgba(0,0,0,0.06)] transition-all duration-400 hover:scale-[1.02]">
              <Star className="w-8 h-8 text-primary mx-auto mb-4" />
              <div className="text-2xl font-royal font-semibold text-foreground mb-2">4.8/5</div>
              <div className="text-base text-muted-foreground font-refined">Avg Rating</div>
            </div>
            <div className="bg-card/80 backdrop-blur-xl rounded-3xl p-8 text-center border border-border/20 shadow-[0_6px_20px_rgba(0,0,0,0.04)] hover:shadow-[0_8px_28px_rgba(0,0,0,0.06)] transition-all duration-400 hover:scale-[1.02]">
              <Shield className="w-8 h-8 text-primary mx-auto mb-4" />
              <div className="text-2xl font-royal font-semibold text-foreground mb-2">12</div>
              <div className="text-base text-muted-foreground font-refined">Verified Vendors</div>
            </div>
            <div className="bg-card/80 backdrop-blur-xl rounded-3xl p-8 text-center border border-border/20 shadow-[0_6px_20px_rgba(0,0,0,0.04)] hover:shadow-[0_8px_28px_rgba(0,0,0,0.06)] transition-all duration-400 hover:scale-[1.02]">
              <TrendingDown className="w-8 h-8 text-primary mx-auto mb-4" />
              <div className="text-2xl font-royal font-semibold text-foreground mb-2">35%</div>
              <div className="text-base text-muted-foreground font-refined">Max Savings</div>
            </div>
          </div>
        </div>
      </div>

      <main className="py-8">
        <div className="max-w-7xl mx-auto px-6">
          {/* Advanced Filter Toggle */}
          <div className="mb-6 flex justify-between items-center">
            <h2 className="text-xl font-semibold text-foreground">Price Comparison Results</h2>
            <button
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
            >
              <Filter className="w-4 h-4" />
              {showAdvancedFilters ? 'Hide' : 'Show'} Advanced Filters
            </button>
          </div>

          {/* Comprehensive Price Comparison System */}
          <PriceComparisonSystem 
            productId={selectedProduct} 
            productName={selectedProductName}
          />
        </div>
      </main>
    </div>
  );
};

export default PriceComparePage;

