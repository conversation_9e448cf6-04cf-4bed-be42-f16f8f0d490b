import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Search, Grid, List, Star, Heart, ShoppingBag, AlertTriangle } from 'lucide-react';
import { supabase } from '../lib/supabase';

interface Product {
  id: string;
  name: string;
  brand: string;
  category: string;
  description?: string;
  image_url: string;
  user_rating_avg: number;
  user_rating_count: number;
  nicotine_strengths: any; // jsonb field from database
  flavors: string[];
  is_verified: boolean;
  ingredients?: string;
  country_of_origin?: string;
  manufacturer?: string;
  tags?: string[];
  expert_notes_chemicals?: string;
  expert_notes_gum_health?: string;
  created_at: string;
  updated_at: string;
}

const SmokelessPage: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedBrand, setSelectedBrand] = useState('all');
  const [selectedStrength, setSelectedStrength] = useState('all');
  const [selectedFlavor, setSelectedFlavor] = useState('all');
  const [selectedCountry, setSelectedCountry] = useState('all');
  const [selectedManufacturer, setSelectedManufacturer] = useState('all');
  const [selectedTag, setSelectedTag] = useState('all');
  const [verifiedOnly, setVerifiedOnly] = useState(false);
  const [hasIngredients, setHasIngredients] = useState(false);
  const [hasExpertNotes, setHasExpertNotes] = useState(false);
  const [minRating, setMinRating] = useState(0);
  const [minReviews, setMinReviews] = useState(0);
  const [sortBy, setSortBy] = useState('rating');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);
  
  console.log('🚨 SmokelessPage: INCREMENTAL RESTORATION - Step 2: Search & Filter functionality added');
  
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        console.log('🚨 SmokelessPage: Fetching real products from database...');
        setLoading(true);
        setError(null);
        
        // Fetch real smokeless products with ALL fields from mission_fresh.smokeless_products table (RULE 0001 compliant)
        // Exclude FDA-approved patches, gums, lozenges, and inhalers which belong in NRT Directory
        const { data, error } = await supabase
          .from('smokeless_products')
          .select(`
            id,
            name,
            brand,
            category,
            description,
            image_url,
            nicotine_strengths,
            flavors,
            user_rating_avg,
            user_rating_count,
            is_verified,
            ingredients,
            country_of_origin,
            manufacturer,
            tags,
            expert_notes_chemicals,
            expert_notes_gum_health,
            created_at,
            updated_at
          `)
          .not('category', 'in', '("Patch","Gum","Lozenge","Inhaler","Nasal Spray")')
          .not('brand', 'ilike', '%NicoDerm%')
          .not('brand', 'ilike', '%Nicorette%')
          .not('brand', 'ilike', '%Commit%')
          .not('name', 'ilike', '%patch%')
          .not('name', 'ilike', '%gum%')
          .not('name', 'ilike', '%lozenge%')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('🚨 SmokelessPage: Database error:', error);
          throw error;
        }
        console.log('🚨 SmokelessPage: Data received:', data?.length || 0, 'products');
        console.log('🚨 SmokelessPage: Sample product data:', data?.[0]);

        setProducts(data || []);
        
      } catch (err) {
        console.error('🚨 SmokelessPage: Error fetching products:', err);
        setError(`Failed to load smokeless products: ${err instanceof Error ? err.message : 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);
  
  // THE MOST SOPHISTICATED SEARCH & FILTER SYSTEM IN THE WORLD
  const filteredProducts = products.filter(product => {
    // 1. ADVANCED TEXT SEARCH - searches ALL text fields
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = !searchTerm || (
      product.name.toLowerCase().includes(searchLower) ||
      product.brand.toLowerCase().includes(searchLower) ||
      product.category.toLowerCase().includes(searchLower) ||
      (product.description && product.description.toLowerCase().includes(searchLower)) ||
      (product.manufacturer && product.manufacturer.toLowerCase().includes(searchLower)) ||
      (product.country_of_origin && product.country_of_origin.toLowerCase().includes(searchLower)) ||
      (product.ingredients && product.ingredients.toLowerCase().includes(searchLower)) ||
      (product.expert_notes_chemicals && product.expert_notes_chemicals.toLowerCase().includes(searchLower)) ||
      (product.expert_notes_gum_health && product.expert_notes_gum_health.toLowerCase().includes(searchLower)) ||
      (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchLower))) ||
      (product.flavors && product.flavors.some(flavor => flavor.toLowerCase().includes(searchLower)))
    );

    // 2. CATEGORY FILTER
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;

    // 3. BRAND FILTER
    const matchesBrand = selectedBrand === 'all' || product.brand === selectedBrand;

    // 4. SOPHISTICATED NICOTINE STRENGTH FILTER
    const matchesStrength = selectedStrength === 'all' || (() => {
      if (!product.nicotine_strengths) return false;

      try {
        if (Array.isArray(product.nicotine_strengths)) {
          return product.nicotine_strengths.some(s => {
            if (typeof s === 'object' && s !== null && s.value && s.unit) {
              return `${s.value}${s.unit}` === selectedStrength;
            }
            return s && s.toString() === selectedStrength;
          });
        }

        if (typeof product.nicotine_strengths === 'object' && product.nicotine_strengths !== null) {
          if (product.nicotine_strengths.value && product.nicotine_strengths.unit) {
            return `${product.nicotine_strengths.value}${product.nicotine_strengths.unit}` === selectedStrength;
          }
          return Object.values(product.nicotine_strengths).some(v =>
            v && v.toString() === selectedStrength
          );
        }

        return product.nicotine_strengths.toString() === selectedStrength;
      } catch (error) {
        return JSON.stringify(product.nicotine_strengths).includes(selectedStrength);
      }
    })();

    // 5. FLAVOR FILTER
    const matchesFlavor = selectedFlavor === 'all' ||
                         (product.flavors && product.flavors.includes(selectedFlavor));

    // 6. COUNTRY OF ORIGIN FILTER
    const matchesCountry = selectedCountry === 'all' ||
                          (product.country_of_origin === selectedCountry);

    // 7. MANUFACTURER FILTER
    const matchesManufacturer = selectedManufacturer === 'all' ||
                               (product.manufacturer === selectedManufacturer);

    // 8. TAG FILTER
    const matchesTag = selectedTag === 'all' ||
                      (product.tags && product.tags.includes(selectedTag));

    // 9. VERIFICATION STATUS FILTER
    const matchesVerified = !verifiedOnly || product.is_verified;

    // 10. INGREDIENTS FILTER
    const matchesIngredients = !hasIngredients || (product.ingredients && product.ingredients.length > 0);

    // 11. EXPERT NOTES FILTER
    const matchesExpertNotes = !hasExpertNotes ||
                              (product.expert_notes_chemicals && product.expert_notes_chemicals.length > 0) ||
                              (product.expert_notes_gum_health && product.expert_notes_gum_health.length > 0);

    // 12. RATING FILTER
    const matchesRating = !product.user_rating_avg || product.user_rating_avg >= minRating;

    // 13. REVIEW COUNT FILTER
    const matchesReviews = !product.user_rating_count || product.user_rating_count >= minReviews;

    return matchesSearch && matchesCategory && matchesBrand && matchesStrength &&
           matchesFlavor && matchesCountry && matchesManufacturer && matchesTag &&
           matchesVerified && matchesIngredients && matchesExpertNotes &&
           matchesRating && matchesReviews;
  });

  // THE MOST SOPHISTICATED SORTING SYSTEM IN THE WORLD
  const sortedProducts = filteredProducts.sort((a, b) => {
    switch (sortBy) {
      case 'rating':
        // Sort by rating, then by review count as tiebreaker
        const ratingDiff = (b.user_rating_avg || 0) - (a.user_rating_avg || 0);
        return ratingDiff !== 0 ? ratingDiff : (b.user_rating_count || 0) - (a.user_rating_count || 0);

      case 'reviews':
        // Sort by review count, then by rating as tiebreaker
        const reviewDiff = (b.user_rating_count || 0) - (a.user_rating_count || 0);
        return reviewDiff !== 0 ? reviewDiff : (b.user_rating_avg || 0) - (a.user_rating_avg || 0);

      case 'newest':
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();

      case 'oldest':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();

      case 'name':
        return a.name.localeCompare(b.name);

      case 'brand':
        // Sort by brand, then by name as tiebreaker
        const brandDiff = a.brand.localeCompare(b.brand);
        return brandDiff !== 0 ? brandDiff : a.name.localeCompare(b.name);

      case 'verified':
        // Verified products first, then by rating
        const verifiedDiff = (b.is_verified ? 1 : 0) - (a.is_verified ? 1 : 0);
        return verifiedDiff !== 0 ? verifiedDiff : (b.user_rating_avg || 0) - (a.user_rating_avg || 0);

      case 'popularity':
        // Combination of rating and review count for popularity score
        const aPopularity = (a.user_rating_avg || 0) * Math.log(1 + (a.user_rating_count || 0));
        const bPopularity = (b.user_rating_avg || 0) * Math.log(1 + (b.user_rating_count || 0));
        return bPopularity - aPopularity;

      default:
        return 0;
    }
  });

  // EXTRACT ALL UNIQUE VALUES FOR THE MOST SOPHISTICATED FILTERS IN THE WORLD
  const categories = [...new Set(products.map(p => p.category))].filter(Boolean).sort();
  const brands = [...new Set(products.map(p => p.brand))].filter(Boolean).sort();
  const countries = [...new Set(products.map(p => p.country_of_origin))].filter(Boolean).sort();
  const manufacturers = [...new Set(products.map(p => p.manufacturer))].filter(Boolean).sort();
  const tags = [...new Set(products.flatMap(p => p.tags || []))].filter(Boolean).sort();

  // Extract strengths from jsonb field - handle various formats
  const strengths = [...new Set(products.flatMap(p => {
    if (!p.nicotine_strengths) return [];

    try {
      if (Array.isArray(p.nicotine_strengths)) {
        return p.nicotine_strengths
          .map(s => {
            if (typeof s === 'object' && s !== null && s.value && s.unit) {
              return `${s.value}${s.unit}`;
            }
            if (typeof s === 'string' || typeof s === 'number') {
              return s.toString();
            }
            return null;
          })
          .filter(s => s && s !== 'null' && s !== 'undefined');
      }

      if (typeof p.nicotine_strengths === 'object' && p.nicotine_strengths !== null) {
        // Handle object format like {unit: "mg", value: "4"}
        if (p.nicotine_strengths.value && p.nicotine_strengths.unit) {
          return [`${p.nicotine_strengths.value}${p.nicotine_strengths.unit}`];
        }
        // Handle other object formats - only extract primitive values
        const values = Object.values(p.nicotine_strengths)
          .filter(v => v && (typeof v === 'string' || typeof v === 'number'))
          .map(v => (v as string | number).toString())
          .filter(v => v && v !== 'null' && v !== 'undefined');
        return values.length > 0 ? values : [];
      }

      if (typeof p.nicotine_strengths === 'string' || typeof p.nicotine_strengths === 'number') {
        const str = p.nicotine_strengths.toString();
        return str && str !== 'null' && str !== 'undefined' ? [str] : [];
      }

      return [];
    } catch (error) {
      console.warn('Error processing nicotine_strengths:', error);
      return [];
    }
  }).filter(s => s && typeof s === 'string' && s.length > 0))].sort();

  const flavors = [...new Set(products.flatMap(p => p.flavors || []))].sort();
  
  return (
    <div className="min-h-screen bg-white">
      {/* Age Verification Warning */}
      <div className="bg-destructive/10 border-b border-destructive/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-3">
            <AlertTriangle className="w-6 h-6 text-destructive flex-shrink-0" />
            <div>
              <p className="text-destructive font-semibold">Age Verification Required</p>
              <p className="text-destructive/80 text-sm">You must be 21+ to view smokeless products. These products contain nicotine and are not approved by the FDA as smoking cessation aids.</p>
            </div>
          </div>
        </div>
      </div>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Enhanced Apple Mac Desktop Premium Header Section */}
        <div className="mb-16">
          <div className="text-center max-w-5xl mx-auto">
            <h1 className="text-6xl sm:text-7xl lg:text-8xl font-royal text-foreground mb-8 tracking-royal leading-[0.9] font-light">
              Smokeless Alternatives
            </h1>
            <p className="text-xl sm:text-2xl lg:text-3xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-sophisticated tracking-graceful">
              Explore nicotine pouches and other smokeless alternatives. These products are NOT FDA-approved
              as nicotine replacement therapy but are popular alternatives to smoking.
            </p>
          </div>
        </div>

        {/* Important Disclaimer */}
        <div className="bg-secondary border border-border rounded-xl p-6 mb-8">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-6 h-6 text-warning flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="font-semibold text-foreground mb-2">Important Medical Disclaimer</h3>
              <p className="text-muted-foreground text-sm leading-relaxed">
                These products are NOT FDA-approved as nicotine replacement therapy. They have not been evaluated 
                for safety or efficacy in smoking cessation. Consult your healthcare provider before using any 
                nicotine products. For FDA-approved NRT products, visit our <Link to="/products" className="underline font-medium">NRT Directory</Link>.
              </p>
            </div>
          </div>
        </div>

        {/* Enhanced Apple Mac Desktop Search and Filter Section */}
        <div className="mb-10">
          {/* Apple Mac Desktop Premium Search Bar */}
          <div className="mb-20">
            <div className="max-w-4xl mx-auto">
              <div className="relative group">
                <Search className="absolute left-8 top-1/2 transform -translate-y-1/2 text-foreground/50 w-7 h-7 group-focus-within:text-primary transition-colors duration-500" />
                <input
                  type="text"
                  placeholder="Search smokeless products, brands, flavors, or strengths..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-18 pr-12 py-7 bg-background/99 backdrop-blur-2xl border border-border/25 rounded-[2rem] text-xl text-foreground placeholder-muted-foreground/40 focus:outline-none focus:ring-3 focus:ring-primary/25 focus:border-primary/35 transition-all duration-500 font-sophisticated tracking-graceful shadow-[0_6px_20px_rgba(0,0,0,0.04)] focus:shadow-[0_8px_28px_rgba(0,0,0,0.06)] hover:border-primary/20 hover:shadow-[0_7px_24px_rgba(0,0,0,0.05)] focus:scale-[1.01]"
                />
                <div className="absolute inset-0 rounded-[2rem] bg-gradient-to-r from-primary/3 to-transparent opacity-0 group-focus-within:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
              </div>
            </div>
          </div>

        {/* Apple Mac Desktop & iOS Mobile Filter Controls */}
        <div className="mb-12 sm:mb-14 lg:mb-16">
          <div className="bg-background/95 backdrop-blur-md rounded-2xl sm:rounded-3xl border border-border/20 shadow-[0_4px_12px_rgba(0,0,0,0.06)] hover:shadow-[0_6px_16px_rgba(0,0,0,0.08)] transition-all duration-300 p-6 sm:p-8 lg:p-10">
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-5 mb-8 sm:mb-10">
              <AlertTriangle className="w-5 h-5 sm:w-6 sm:h-6 text-destructive" />
              <h2 className="text-xl sm:text-2xl font-royal text-foreground tracking-royal">Filter Smokeless Products</h2>
              <span className="text-sm sm:text-base text-muted-foreground font-sophisticated tracking-elegant">21+ Age Verification Required</span>
            </div>

            {/* Enhanced Filters Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
              {/* Category Filter */}
              <div>
                <label className="text-sm sm:text-base font-refined text-muted-foreground tracking-refined mb-3 block">PRODUCT CATEGORY</label>
                <select
                  name="category"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-4 sm:px-5 py-3 sm:py-4 bg-background/98 backdrop-blur-sm border border-border/30 rounded-xl sm:rounded-2xl text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/40 transition-all duration-300 font-sophisticated text-base tracking-elegant shadow-[0_2px_6px_rgba(0,0,0,0.04)] focus:shadow-[0_4px_12px_rgba(0,0,0,0.08)] hover:border-border/50"
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Brand Filter - Apple Style Consistent */}
              <div>
                <label className="text-sm sm:text-base font-refined text-muted-foreground tracking-refined mb-3 block">BRAND</label>
                <select
                  name="brand"
                  value={selectedBrand}
                  onChange={(e) => setSelectedBrand(e.target.value)}
                  className="w-full px-4 sm:px-5 py-3 sm:py-4 bg-background/98 backdrop-blur-sm border border-border/30 rounded-xl sm:rounded-2xl text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/40 transition-all duration-300 font-sophisticated text-base tracking-elegant shadow-[0_2px_6px_rgba(0,0,0,0.04)] focus:shadow-[0_4px_12px_rgba(0,0,0,0.08)] hover:border-border/50"
                >
                  <option value="all">All Brands</option>
                  {brands.map(brand => (
                    <option key={brand} value={brand}>{brand}</option>
                  ))}
                </select>
              </div>

              {/* Nicotine Strength Filter - Apple Style Consistent */}
              <div>
                <label className="text-sm sm:text-base font-refined text-muted-foreground tracking-refined mb-3 block">NICOTINE STRENGTH</label>
                <select
                  name="strength"
                  value={selectedStrength}
                  onChange={(e) => setSelectedStrength(e.target.value)}
                  className="w-full px-4 sm:px-5 py-3 sm:py-4 bg-background/98 backdrop-blur-sm border border-border/30 rounded-xl sm:rounded-2xl text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/40 transition-all duration-300 font-sophisticated text-base tracking-elegant shadow-[0_2px_6px_rgba(0,0,0,0.04)] focus:shadow-[0_4px_12px_rgba(0,0,0,0.08)] hover:border-border/50"
                >
                  <option value="all">All Strengths</option>
                  {strengths.map(strength => (
                    <option key={strength || 'unknown'} value={strength || ''}>{strength || 'Unknown'}</option>
                  ))}
                </select>
              </div>

              {/* Rating Filter - Apple Style Consistent */}
              <div>
                <label className="text-sm sm:text-base font-refined text-muted-foreground tracking-refined mb-3 block">MINIMUM RATING</label>
                <select
                  name="minRating"
                  value={minRating}
                  onChange={(e) => setMinRating(Number(e.target.value))}
                  className="w-full px-4 sm:px-5 py-3 sm:py-4 bg-background/98 backdrop-blur-sm border border-border/30 rounded-xl sm:rounded-2xl text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/40 transition-all duration-300 font-sophisticated text-base tracking-elegant shadow-[0_2px_6px_rgba(0,0,0,0.04)] focus:shadow-[0_4px_12px_rgba(0,0,0,0.08)] hover:border-border/50"
                >
                  <option value={0}>All Ratings</option>
                  <option value={1}>1+ Stars</option>
                  <option value={2}>2+ Stars</option>
                  <option value={3}>3+ Stars</option>
                  <option value={4}>4+ Stars</option>
                  <option value={4.5}>4.5+ Stars</option>
                </select>
              </div>
            </div>
          </div>
        </div>

          {/* Advanced Filters Toggle */}
          <div className="mb-6">
            <button
              onClick={() => setAdvancedFiltersOpen(!advancedFiltersOpen)}
              className="flex items-center gap-2 px-4 py-2 bg-wellness text-white rounded-xl hover:bg-wellness/90 transition-colors"
            >
              <span>Advanced Filters</span>
              <span className={`transform transition-transform ${advancedFiltersOpen ? 'rotate-180' : ''}`}>▼</span>
            </button>
          </div>

          {/* Advanced Filters Section */}
          {advancedFiltersOpen && (
            <div className="bg-white rounded-2xl p-6 mb-6">
              <h3 className="text-lg font-semibold text-wellness mb-4">Advanced Search & Filter Options</h3>

              {/* Advanced Filters Grid */}
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
                {/* Country Filter */}
                <div>
                  <label className="block text-sm font-medium text-wellness mb-2">Country of Origin</label>
                  <select
                    value={selectedCountry}
                    onChange={(e) => setSelectedCountry(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-wellness focus:border-transparent"
                  >
                    <option value="all">All Countries</option>
                    {countries.map(country => (
                      <option key={country} value={country}>{country}</option>
                    ))}
                  </select>
                </div>

                {/* Manufacturer Filter */}
                <div>
                  <label className="block text-sm font-medium text-wellness mb-2">Manufacturer</label>
                  <select
                    value={selectedManufacturer}
                    onChange={(e) => setSelectedManufacturer(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-wellness focus:border-transparent"
                  >
                    <option value="all">All Manufacturers</option>
                    {manufacturers.map(manufacturer => (
                      <option key={manufacturer} value={manufacturer}>{manufacturer}</option>
                    ))}
                  </select>
                </div>

                {/* Tag Filter */}
                <div>
                  <label className="block text-sm font-medium text-wellness mb-2">Product Tags</label>
                  <select
                    value={selectedTag}
                    onChange={(e) => setSelectedTag(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-wellness focus:border-transparent"
                  >
                    <option value="all">All Tags</option>
                    {tags.map(tag => (
                      <option key={tag} value={tag}>{tag}</option>
                    ))}
                  </select>
                </div>

                {/* Minimum Reviews Filter */}
                <div>
                  <label className="block text-sm font-medium text-wellness mb-2">Minimum Reviews</label>
                  <select
                    value={minReviews}
                    onChange={(e) => setMinReviews(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-wellness focus:border-transparent"
                  >
                    <option value={0}>Any Number</option>
                    <option value={5}>5+ Reviews</option>
                    <option value={10}>10+ Reviews</option>
                    <option value={25}>25+ Reviews</option>
                    <option value={50}>50+ Reviews</option>
                    <option value={100}>100+ Reviews</option>
                  </select>
                </div>
              </div>

              {/* Boolean Filters */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Verified Only */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="verifiedOnly"
                    checked={verifiedOnly}
                    onChange={(e) => setVerifiedOnly(e.target.checked)}
                    className="w-4 h-4 text-wellness bg-white border-gray-300 rounded focus:ring-wellness focus:ring-2"
                  />
                  <label htmlFor="verifiedOnly" className="ml-2 text-sm font-medium text-wellness">
                    Verified Products Only
                  </label>
                </div>

                {/* Has Ingredients */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="hasIngredients"
                    checked={hasIngredients}
                    onChange={(e) => setHasIngredients(e.target.checked)}
                    className="w-4 h-4 text-wellness bg-white border-gray-300 rounded focus:ring-wellness focus:ring-2"
                  />
                  <label htmlFor="hasIngredients" className="ml-2 text-sm font-medium text-wellness">
                    Has Ingredient Information
                  </label>
                </div>

                {/* Has Expert Notes */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="hasExpertNotes"
                    checked={hasExpertNotes}
                    onChange={(e) => setHasExpertNotes(e.target.checked)}
                    className="w-4 h-4 text-wellness bg-white border-gray-300 rounded focus:ring-wellness focus:ring-2"
                  />
                  <label htmlFor="hasExpertNotes" className="ml-2 text-sm font-medium text-wellness">
                    Has Expert Analysis
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* View Mode Toggle */}
          <div className="flex items-center gap-4 mb-6">
            <span className="text-sm font-medium text-wellness">View:</span>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg transition-colors ${
                  viewMode === 'grid' ? 'bg-wellness text-white' : 'bg-white text-wellness hover:bg-gray-200'
                }`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg transition-colors ${
                  viewMode === 'list' ? 'bg-wellness text-white' : 'bg-white text-wellness hover:bg-gray-200'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-sm text-wellness">
            Showing {sortedProducts.length} of {products.length} smokeless products
          </p>
        </div>

        {/* Products Grid/List */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-background rounded-2xl shadow-sm border border-wellness p-8 animate-pulse">
                <div className="w-full h-48 bg-muted rounded-xl mb-4"></div>
                <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-muted rounded w-1/2"></div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="text-alert-red mb-4">
              <AlertTriangle className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold text-wellness mb-2">Error Loading Products</h3>
            <p className="text-wellness">{error}</p>
          </div>
        ) : sortedProducts.length === 0 ? (
          <div className="text-center py-12">
            <Search className="w-12 h-12 text-wellness mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-wellness mb-2">No Smokeless Products Found</h3>
            <p className="text-wellness">Try adjusting your filters or check back later.</p>
          </div>
        ) : (
          <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
            {sortedProducts.map(product => (
              <Link
                key={product.id}
                to={`/product/${product.id}`}
                className={`block bg-white rounded-2xl shadow-sm border border-wellness hover:shadow-xl hover:border-wellness transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 cursor-pointer ${
                  viewMode === 'list' ? 'flex items-center p-6' : 'p-6'
                }`}
              >
                <div className={viewMode === 'list' ? 'w-24 h-24 mr-6 flex-shrink-0' : 'w-full'}>
                  <div className="relative">
                    {product.image_url && product.image_url.trim() !== '' ? (
                      <img
                        src={product.image_url}
                        alt={product.name}
                        className={`rounded-xl object-cover ${viewMode === 'list' ? 'w-24 h-24' : 'w-full h-48'}`}
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                          e.currentTarget.parentElement?.querySelector('.fallback-icon')?.classList.remove('hidden');
                        }}
                      />
                    ) : null}
                    <div className={`fallback-icon ${product.image_url && product.image_url.trim() !== '' ? 'hidden' : ''} w-full h-48 bg-muted rounded-xl flex items-center justify-center`}>
                      <ShoppingBag className="w-16 h-16 text-muted-foreground" />
                    </div>
                    {/* Real Database Rating Score */}
                    <div className="absolute top-2 right-2 bg-background/90 backdrop-blur-sm rounded-lg px-2 py-1">
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                        <span className="text-xs font-medium text-wellness">
                          {product.user_rating_avg ? (product.user_rating_avg * 2).toFixed(1) : '0.0'}/10
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className={viewMode === 'list' ? 'flex-1' : 'mt-4'}>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-xs bg-warning/10 text-warning px-2 py-1 rounded-full font-medium">
                      Not FDA-Approved
                    </span>
                  </div>
                  
                  <h3 className="font-semibold text-wellness mb-1">{product.name}</h3>
                  <p className="text-sm text-wellness mb-3">{product.brand}</p>
                  
                  <div className="flex items-center gap-4 mb-3">
                    <div className="flex items-center gap-1">
                      {[...Array(5)].map((_, i) => (
                        <Star 
                          key={i} 
                          className={`w-4 h-4 ${
                            product.user_rating_avg && i < Math.floor(product.user_rating_avg) 
                              ? 'text-yellow-400 fill-current' 
                              : 'text-gray-300'
                          }`} 
                        />
                      ))}
                      <span className="text-sm font-medium text-wellness ml-1">
                        {product.user_rating_avg ? product.user_rating_avg.toFixed(1) : 'No rating'}
                      </span>
                    </div>
                    <span className="text-sm text-wellness">({product.user_rating_count || 0} reviews)</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-wellness">
                      {(() => {
                        if (!product.nicotine_strengths) return 'Various strengths';

                        if (Array.isArray(product.nicotine_strengths)) {
                          const first = product.nicotine_strengths[0];
                          if (typeof first === 'object' && first && first.value && first.unit) {
                            return `${first.value}${first.unit}`;
                          }
                          return first ? first.toString() : 'Various strengths';
                        }

                        if (typeof product.nicotine_strengths === 'object') {
                          // Handle {unit: "mg", value: "4"} format
                          if (product.nicotine_strengths.value && product.nicotine_strengths.unit) {
                            return `${product.nicotine_strengths.value}${product.nicotine_strengths.unit}`;
                          }
                          // Fallback for other object formats - only use primitive values
                          const values = Object.values(product.nicotine_strengths)
                            .filter(v => v && v !== null && (typeof v === 'string' || typeof v === 'number'))
                            .map(v => (v as string | number).toString());
                          return values.length > 0 ? values.join(' ') : 'Various strengths';
                        }

                        return product.nicotine_strengths.toString();
                      })()}
                    </span>
                    <div className="flex items-center gap-2">
                      <Heart className="w-4 h-4 text-wellness hover:text-red-500 transition-colors cursor-pointer" />
                      <ShoppingBag className="w-4 h-4 text-wellness hover:text-wellness transition-colors cursor-pointer" />
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </main>
    </div>
  );
};

export default SmokelessPage;
