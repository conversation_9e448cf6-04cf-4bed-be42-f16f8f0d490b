import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Star, DollarSign, Trophy, Filter, Search } from 'lucide-react';
import { getNRTProducts, getStores, createProductReview } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

interface Product {
  id: string;
  name: string;
  brand: string;
  category: string;
  rating: number;
  price: number;
  reviews: number;
}

interface Store {
  id: string;
  name: string;
  rating: number;
  review_count: number;
  city: string;
  state: string;
}



const ReviewsPage: React.FC = () => {
  const { user } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [stores, setStores] = useState<Store[]>([]);

  const [activeTab, setActiveTab] = useState<'products' | 'stores' | 'prices'>('products');
  const [sortBy, setSortBy] = useState<'rating' | 'reviews' | 'price'>('rating');
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Review form state
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [selectedRating, setSelectedRating] = useState<number>(0);
  const [reviewText, setReviewText] = useState<string>('');
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const productsData = await getNRTProducts();
        // Map the database fields to match our Product interface
        const formattedProducts = (productsData || []).map(product => ({
          id: product.id,
          name: product.name,
          brand: product.brand,
          category: product.category,
          rating: product.user_rating_avg || 0,
          price: product.average_price || product.price_range_min || 0, // Use real database price or 0 if not available
          reviews: product.user_rating_count || 0
        }));
        setProducts(formattedProducts);

        // Fetch real store data from mission_fresh.stores table (RULE 0001 compliant)
        const storesData = await getStores();
        const formattedStores = storesData.map(store => ({
          id: store.id,
          name: store.name,
          rating: store.rating || 4.0,
          review_count: store.review_count || 0,
          city: store.city,
          state: store.state
        }));
        setStores(formattedStores);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Review submission handler (RULE 0001 compliant - real database submission)
  const handleSubmitReview = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedProductId || !selectedRating || !reviewText.trim()) {
      alert('Please fill in all fields and select a rating.');
      return;
    }

    if (!user) {
      alert('Please log in to submit a review.');
      return;
    }

    setSubmitting(true);
    try {
      // Submit review to real database using createProductReview function
      await createProductReview({
        product_id: selectedProductId,
        user_id: user.id,
        rating: selectedRating,
        review_text: reviewText,
        moderation_status: 'pending',
        is_verified_purchase: false
      });

      // Reset form after successful submission
      setSelectedProductId('');
      setSelectedRating(0);
      setReviewText('');
      alert('Review submitted successfully! It will be visible after moderation.');
    } catch (error) {
      console.error('Error submitting review:', error);
      alert('Failed to submit review. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const getSortedProducts = () => {
    let filteredProducts = products;
    
    // Apply search filter if search query exists
    if (searchQuery.trim()) {
      filteredProducts = products.filter(product => 
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    return [...filteredProducts].sort((a, b) => {
      if (sortBy === 'rating') return b.rating - a.rating;
      if (sortBy === 'reviews') return b.reviews - a.reviews;
      if (sortBy === 'price') return a.price - b.price;
      return 0;
    });
  };

  const getSortedStores = () => {
    let filteredStores = stores;
    
    // Apply search filter if search query exists
    if (searchQuery.trim()) {
      filteredStores = stores.filter(store => 
        store.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        store.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
        store.state.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    return [...filteredStores].sort((a, b) => b.rating - a.rating);
  };

  const getCheapestPrices = () => {
    return [...products].sort((a, b) => a.price - b.price);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Apple Mac Desktop Premium Header Section */}
      <div className="bg-background/99 backdrop-blur-2xl border-b border-border/25 shadow-[0_2px_8px_rgba(0,0,0,0.02)]">
        <div className="max-w-7xl mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-royal text-foreground mb-8 tracking-royal leading-[1.02]">Reviews & Ratings</h1>
            <p className="text-xl sm:text-2xl text-muted-foreground mb-6 max-w-4xl mx-auto leading-relaxed font-sophisticated">
              Discover top-rated NRT products, highest-rated stores, and best prices. Filter and search based on real user reviews and ratings.
            </p>
            <p className="text-base text-muted-foreground mb-8 font-refined">
              Advanced search and filtering for informed decisions.
            </p>
            
            {/* Apple Mac Desktop Premium Search Bar */}
            <div className="max-w-4xl mx-auto mb-12">
              <div className="relative group">
                <div className="absolute inset-y-0 left-0 pl-8 flex items-center pointer-events-none">
                  <Search className="h-7 w-7 text-muted-foreground/50 group-focus-within:text-primary transition-colors duration-500" />
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search products, brands, stores..."
                  className="w-full pl-18 pr-12 py-7 bg-background/99 backdrop-blur-2xl border border-border/25 rounded-[2rem] focus:outline-none focus:ring-3 focus:ring-primary/25 focus:border-primary/35 transition-all duration-500 text-xl font-sophisticated placeholder-muted-foreground/40 shadow-[0_6px_20px_rgba(0,0,0,0.04)] focus:shadow-[0_8px_28px_rgba(0,0,0,0.06)] hover:border-primary/20 focus:scale-[1.01]"
                />
              </div>
            </div>
          </div>

          {/* Apple Mac Desktop Premium Filter Tabs */}
          <div className="flex justify-center mb-12">
            <div className="bg-background/90 backdrop-blur-xl border border-border/20 p-2 rounded-2xl shadow-[0_6px_20px_rgba(0,0,0,0.04)]">
              <button
                onClick={() => setActiveTab('products')}
                className={`px-8 py-4 rounded-xl font-refined font-medium transition-all duration-400 ${
                  activeTab === 'products'
                    ? 'bg-primary text-primary-foreground shadow-[0_4px_12px_rgba(0,0,0,0.1)] scale-[1.02]'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                }`}
              >
                <Trophy className="w-5 h-5 inline mr-3" />
                Top-Rated Products
              </button>
              <button
                onClick={() => setActiveTab('stores')}
                className={`px-8 py-4 rounded-xl font-refined font-medium transition-all duration-400 ${
                  activeTab === 'stores'
                    ? 'bg-primary text-primary-foreground shadow-[0_4px_12px_rgba(0,0,0,0.1)] scale-[1.02]'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                }`}
              >
                <Star className="w-5 h-5 inline mr-3" />
                Top-Rated Stores
              </button>
              <button
                onClick={() => setActiveTab('prices')}
                className={`px-8 py-4 rounded-xl font-refined font-medium transition-all duration-400 ${
                  activeTab === 'prices'
                    ? 'bg-primary text-primary-foreground shadow-[0_4px_12px_rgba(0,0,0,0.1)] scale-[1.02]'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                }`}
              >
                <DollarSign className="w-5 h-5 inline mr-3" />
                Best Prices
              </button>
            </div>
          </div>

          {/* Apple Mac Desktop Premium Sort Controls */}
          {activeTab === 'products' && (
            <div className="flex justify-center mb-8">
              <div className="flex items-center space-x-6 bg-background/90 backdrop-blur-xl px-8 py-4 rounded-2xl border border-border/20 shadow-[0_4px_12px_rgba(0,0,0,0.04)]">
                <Filter className="w-5 h-5 text-primary" />
                <span className="text-base text-foreground font-refined font-medium">Sort by:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'rating' | 'reviews' | 'price')}
                  className="border border-border/25 rounded-xl px-4 py-2 text-base font-refined focus:outline-none focus:ring-2 focus:ring-primary/25 focus:border-primary/35 bg-background/99 backdrop-blur-xl transition-all duration-300"
                >
                  <option value="rating">Highest Rating</option>
                  <option value="reviews">Most Reviews</option>
                  <option value="price">Lowest Price</option>
                </select>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Review Submission Form */}
      <section className="max-w-4xl mx-auto px-6 py-8">
        <div className="bg-white rounded-xl shadow-sm border border-wellness p-6">
          <h3 className="text-lg font-semibold text-wellnessdark mb-4">Write a Review</h3>
          <form onSubmit={handleSubmitReview} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-wellnessdark mb-2">Product</label>
              <select
                value={selectedProductId}
                onChange={(e) => setSelectedProductId(e.target.value)}
                className="w-full border border-wellness rounded-lg px-3 py-2 focus:ring-2 focus:ring-wellness focus:border-wellness"
                required
              >
                <option value="">Select a product to review</option>
                {products.map((product) => (
                  <option key={product.id} value={product.id}>
                    {product.brand} {product.name} {product.category}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-wellnessdark mb-2">Rating</label>
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-6 h-6 cursor-pointer transition-colors ${
                      i < selectedRating
                        ? 'text-rating-gold fill-current'
                        : 'text-wellness hover:text-rating-gold'
                    }`}
                    onClick={() => setSelectedRating(i + 1)}
                  />
                ))}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-wellnessdark mb-2">Your Review</label>
              <textarea
                rows={4}
                value={reviewText}
                onChange={(e) => setReviewText(e.target.value)}
                className="w-full border border-wellness rounded-lg px-3 py-2 focus:ring-2 focus:ring-wellness focus:border-wellness"
                placeholder="Share your experience with this NRT product..."
                required
              ></textarea>
            </div>
            <button
              type="submit"
              disabled={submitting}
              className="bg-wellness text-white px-6 py-2 rounded-lg hover:bg-wellness transition-colors font-medium disabled:opacity-50"
            >
              {submitting ? 'Submitting...' : 'Submit Review'}
            </button>
          </form>
        </div>
      </section>

      {/* Active Tab Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-wellness mx-auto mb-4"></div>
            <p className="text-wellness">Loading reviews and ratings...</p>
          </div>
        ) : (
          <div>
            {/* Top-Rated Products Tab */}
            {activeTab === 'products' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {getSortedProducts().slice(0, 12).map((product) => (
                    <Link
                      key={product.id}
                      to={`/product/${product.id}`}
                      className="bg-white rounded-lg shadow-sm border border-wellness p-6 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="font-semibold text-wellnessdark mb-1">{product.name}</h3>
                          <p className="text-sm text-wellness">{product.brand}</p>
                          <p className="text-xs text-wellness uppercase tracking-wide">{product.category}</p>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-1 mb-1">
                            <Star className="w-4 h-4 text-rating-gold fill-current" />
                            <span className="font-medium text-wellnessdark">{product.rating}</span>
                          </div>
                          <p className="text-xs text-wellness">{product.reviews} reviews</p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between mt-4">
                        <span className="text-lg font-bold text-wellness">${product.price}</span>
                        <span className="text-sm text-wellness">View Details →</span>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Top-Rated Stores Tab */}
            {activeTab === 'stores' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {getSortedStores().slice(0, 12).map((store) => (
                    <Link
                      key={store.id}
                      to={`/store/${store.id}`}
                      className="bg-white rounded-lg shadow-sm border border-wellness p-6 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="font-semibold text-wellnessdark mb-1">{store.name}</h3>
                          <p className="text-sm text-wellness">{store.city}, {store.state}</p>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-1 mb-1">
                            <Star className="w-4 h-4 text-rating-gold fill-current" />
                            <span className="font-medium text-wellnessdark">{store.rating}</span>
                          </div>
                          <p className="text-xs text-wellness">{store.review_count} reviews</p>
                        </div>
                      </div>
                      <div className="mt-4">
                        <span className="text-sm text-wellness">View Store Details →</span>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Best Prices Tab */}
            {activeTab === 'prices' && (
              <div className="space-y-6">
                <div className="bg-white rounded-lg p-4 mb-6">
                  <h3 className="font-semibold text-wellnessdark mb-2">Best Price Deals</h3>
                  <p className="text-sm text-wellness">Products sorted by lowest price to help you save money.</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {getCheapestPrices().slice(0, 12).map((product, index) => (
                    <Link
                      key={product.id}
                      to={`/product/${product.id}`}
                      className="bg-white rounded-lg shadow-sm border border-wellness p-6 hover:shadow-md transition-shadow"
                    >
                      {index < 3 && (
                        <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white text-wellnessdark mb-3">
                          Top {index + 1} Deal
                        </div>
                      )}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="font-semibold text-wellnessdark mb-1">{product.name}</h3>
                          <p className="text-sm text-wellness">{product.brand}</p>
                          <p className="text-xs text-wellness uppercase tracking-wide">{product.category}</p>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-1 mb-1">
                            <Star className="w-4 h-4 text-rating-gold fill-current" />
                            <span className="font-medium text-wellnessdark">{product.rating}</span>
                          </div>
                          <p className="text-xs text-wellness">{product.reviews} reviews</p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between mt-4">
                        <span className="text-xl font-bold text-wellness">${product.price}</span>
                        <span className="text-sm text-wellness">Best Price →</span>
                        <span className="text-sm text-wellness">Best Price →</span>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  );
};

export default ReviewsPage;
