import React, { useState, useEffect } from 'react';
import { Target, Calendar, TrendingUp, Award, Clock, Activity, Heart, DollarSign } from 'lucide-react';
import { getUserProgress, getHealthBenefits, getMilestones, UserProgress, HealthBenefit, Milestone } from '../lib/progressApi';

const ProgressPage: React.FC = () => {
  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);
  const [healthBenefits, setHealthBenefits] = useState<HealthBenefit[]>([]);
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);

  useEffect(() => {
    const loadProgressData = async () => {
      try {
        setLoading(true);
        const progressData = await getUserProgress();
        setUserProgress(progressData);
        
        // Calculate days since quit
        const quitDate = new Date(progressData.quit_date);
        const daysSinceQuit = Math.floor((Date.now() - quitDate.getTime()) / (1000 * 60 * 60 * 24));
        
        // Get health benefits and milestones based on progress
        const benefits = getHealthBenefits(daysSinceQuit);
        const userMilestones = getMilestones(daysSinceQuit, progressData.milestones_achieved);
        
        setHealthBenefits(benefits);
        setMilestones(userMilestones);
        setError(null);
      } catch (error) {
        console.error('Progress page error:', error);
        if (error instanceof Error && error.message.includes('database table missing')) {
          setError('Progress tracking feature is not available yet. The user progress database is being set up.');
        } else {
          setError(error instanceof Error ? error.message : 'Failed to load progress data');
        }
      } finally {
        setLoading(false);
      }
    };
    loadProgressData();
  }, []);

  const handleShareProgress = () => {
    if (userProgress) {
      const shareText = `🎉 I've been smoke-free for ${userProgress.current_streak} days! I've saved $${userProgress.total_saved_money.toFixed(2)} and avoided ${userProgress.total_saved_cigarettes} cigarettes. #SmokeFree #HealthyLiving`;

      if (navigator.share) {
        navigator.share({
          title: 'My Smoke-Free Progress',
          text: shareText,
          url: window.location.href
        });
      } else {
        navigator.clipboard.writeText(shareText);
        // Progress copied to clipboard - could show elegant modal here
      }
    }
  };

  const handleViewReport = () => {
    setShowReportModal(true);
  };

  const refreshProgress = async () => {
    setLoading(true);
    try {
      const progressData = await getUserProgress();
      setUserProgress(progressData);

      const quitDate = new Date(progressData.quit_date);
      const daysSinceQuit = Math.floor((Date.now() - quitDate.getTime()) / (1000 * 60 * 60 * 24));

      const benefits = getHealthBenefits(daysSinceQuit);
      const userMilestones = getMilestones(daysSinceQuit, progressData.milestones_achieved);

      setHealthBenefits(benefits);
      setMilestones(userMilestones);
      setError(null);
    } catch (error) {
      console.error('Progress refresh error:', error);
      if (error instanceof Error && error.message.includes('database table missing')) {
        setError('Progress tracking feature is not available yet. The user progress database is being set up.');
      } else {
        setError(error instanceof Error ? error.message : 'Failed to refresh progress data');
      }
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className="bg-background">
      {/* Apple Mac Desktop Premium Header */}
      <header className="bg-background/95 backdrop-blur-2xl border-b border-border/25">
        <div className="max-w-7xl mx-auto px-8 py-16">
          <div className="text-center">
            <div className="inline-flex items-center gap-3 bg-primary/10 px-6 py-3 rounded-2xl mb-8 shadow-[0_4px_12px_rgba(0,0,0,0.04)]">
              <Target className="w-6 h-6 text-primary" />
              <span className="text-primary font-refined text-base">Progress Tracking</span>
            </div>
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-royal text-foreground mb-8 tracking-royal leading-[1.02]">
              Track Your Quit Journey Progress
            </h1>
            <p className="text-xl sm:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed mb-10 font-sophisticated">
              Monitor your smoking cessation progress with comprehensive analytics, milestone tracking,
              health benefit timelines, and detailed insights to keep you motivated and on track for success.
            </p>
            <button
              onClick={refreshProgress}
              className="bg-primary text-primary-foreground px-8 py-4 rounded-2xl hover:bg-primary/90 font-refined transition-all duration-300 text-lg shadow-[0_6px_20px_rgba(0,0,0,0.1)] hover:shadow-[0_8px_28px_rgba(0,0,0,0.15)] hover:scale-[1.02]"
            >
              Refresh Progress Data
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-8 py-16">

        {/* Apple Mac Desktop Premium Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-20">
            <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-4 text-muted-foreground font-sophisticated text-lg">Loading your comprehensive progress data and analytics...</span>
          </div>
        )}

        {/* Apple Mac Desktop Premium Error State */}
        {error && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-[2rem] p-10 mb-12 shadow-[0_8px_24px_rgba(0,0,0,0.04)]">
            <div className="flex items-center gap-6">
              <div className="w-16 h-16 bg-destructive/10 rounded-3xl flex items-center justify-center">
                <span className="text-destructive text-2xl font-bold">!</span>
              </div>
              <div>
                <p className="text-destructive font-refined font-medium text-xl">Unable to load progress data</p>
                <p className="text-muted-foreground text-base mt-2 font-sophisticated">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Progress Data */}
        {!loading && !error && userProgress && (
          <>
            {/* Progress Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              <div
                className="bg-background/98 backdrop-blur-md rounded-2xl p-6 border border-border/20 cursor-pointer hover:border-border/40 hover:shadow-[0_8px_24px_rgba(0,0,0,0.08)] transition-all duration-300 group"
                onClick={() => alert(`You've been smoke-free for ${userProgress.current_streak} days! Your quit date was ${new Date(userProgress.quit_date).toLocaleDateString()}.`)}
              >
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary/15 to-primary/5 rounded-2xl flex items-center justify-center shadow-[0_3px_8px_rgba(34,197,94,0.12)] border border-primary/10 group-hover:scale-105 transition-transform duration-300">
                    <Calendar className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <div className="text-2xl font-royal text-foreground tracking-royal">{userProgress.current_streak}</div>
                    <div className="text-sm text-muted-foreground font-sophisticated tracking-elegant">Days Smoke-Free</div>
                  </div>
                </div>
                <div className="text-sm text-primary font-refined tracking-refined">+1 day from yesterday</div>
              </div>

              <div
                className="bg-background/98 backdrop-blur-md rounded-2xl p-6 border border-border/20 cursor-pointer hover:border-border/40 hover:shadow-[0_8px_24px_rgba(0,0,0,0.08)] transition-all duration-300 group"
                onClick={() => alert(`You've saved $${userProgress.total_saved_money.toFixed(2)} since quitting! This could buy you something special instead.`)}
              >
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary/15 to-primary/5 rounded-2xl flex items-center justify-center shadow-[0_3px_8px_rgba(34,197,94,0.12)] border border-primary/10 group-hover:scale-105 transition-transform duration-300">
                    <DollarSign className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <div className="text-2xl font-royal text-foreground tracking-royal">${userProgress.total_saved_money.toFixed(2)}</div>
                    <div className="text-sm text-muted-foreground font-sophisticated tracking-elegant">Money Saved</div>
                  </div>
                </div>
                <div className="text-sm text-primary font-refined tracking-refined">Keep it up!</div>
              </div>

              <div
                className="bg-background rounded-lg p-4 border border-muted cursor-pointer hover:border-accent transition-all"
                onClick={() => alert(`You've avoided ${userProgress.total_saved_cigarettes} cigarettes! That's amazing for your health and lungs.`)}
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                    <Heart className="w-5 h-5 text-background" />
                  </div>
                  <div>
                    <div className="text-lg font-refined text-foreground">{userProgress.total_saved_cigarettes}</div>
                    <div className="text-xs text-muted-foreground font-refined">Cigarettes Avoided</div>
                  </div>
                </div>
                <div className="text-xs text-primary font-refined">Excellent progress</div>
              </div>

              <div
                className="bg-background rounded-lg p-4 border border-muted cursor-pointer hover:border-accent transition-all"
                onClick={() => alert(`You've achieved ${userProgress.milestones_achieved.length} milestones! Each one represents a significant step in your journey.`)}
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                    <Award className="w-5 h-5 text-background" />
                  </div>
                  <div>
                    <div className="text-lg font-refined text-foreground">{userProgress.milestones_achieved.length}</div>
                    <div className="text-sm text-muted-foreground font-medium">Milestones</div>
                  </div>
                </div>
                <div className="text-sm text-primary font-medium">Amazing journey</div>
              </div>
            </div>
          </>
        )}

        {/* Progress Chart Section */}
        <section className="mb-8">
          <div className="bg-background rounded-lg p-4 border border-muted">
            <h2 className="text-lg font-refined text-foreground mb-4 tracking-refined">Daily Progress Chart</h2>

            {/* Apple Mac Desktop Chart Area */}
            <div className="h-48 bg-muted/30 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <TrendingUp className="w-12 h-12 text-foreground mx-auto mb-3" />
                <h3 className="text-sm font-refined text-foreground mb-2">Interactive Progress Chart</h3>
                <p className="text-muted-foreground font-refined text-xs">
                  Visual representation of your cessation journey with detailed analytics
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Milestones Section */}
        {!loading && !error && userProgress && (
          <section className="mb-8">
            <h2 className="text-lg font-refined text-foreground mb-4 tracking-refined">Achievement Milestones</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {milestones.map((milestone) => {
                const quitDate = new Date(userProgress.quit_date);
                const daysSinceQuit = Math.floor((Date.now() - quitDate.getTime()) / (1000 * 60 * 60 * 24));
                const daysUntilMilestone = milestone.achievement_criteria.days_required ? milestone.achievement_criteria.days_required - daysSinceQuit : 0;

                return (
                  <div
                    key={milestone.id}
                    className={`bg-background rounded-lg p-4 border border-muted relative cursor-pointer hover:border-accent transition-all ${
                      !milestone.is_achieved ? 'opacity-75' : ''
                    }`}
                    onClick={() => alert(`${milestone.name}: ${milestone.description}${milestone.is_achieved ? ' - Completed!' : ` - ${daysUntilMilestone} days remaining`}`)}
                  >
                    <div className="absolute top-3 right-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                        milestone.is_achieved
                          ? 'bg-primary'
                          : 'bg-muted border border-border'
                      }`}>
                        {milestone.is_achieved ? (
                          <Award className="w-5 h-5 text-white" />
                        ) : (
                          <Clock className="w-5 h-5 text-muted-foreground" />
                        )}
                      </div>
                    </div>
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold text-foreground mb-2">{milestone.name}</h3>
                      <p className="text-sm text-muted-foreground">{milestone.description}</p>
                    </div>
                    <div className={`text-sm font-medium ${
                      milestone.is_achieved ? 'text-primary' : 'text-muted-foreground'
                    }`}>
                      {milestone.is_achieved
                        ? 'Completed!'
                        : `${daysUntilMilestone} days remaining`
                      }
                    </div>
                  </div>
                );
              })}
            </div>
          </section>
        )}

        {/* Health Benefits Timeline */}
        {!loading && !error && userProgress && (
          <section className="mb-16">
            <div className="bg-card rounded-xl p-8 shadow-sm border border-border">
              <h2 className="text-2xl font-royal text-foreground mb-8">Health Benefits Timeline</h2>
              
              <div className="space-y-8">
                {healthBenefits.map((benefit) => {
                  const IconComponent = benefit.icon === 'Heart' ? Heart :
                                      benefit.icon === 'Activity' ? Activity :
                                      benefit.icon === 'TrendingUp' ? TrendingUp : Heart;

                  return (
                    <div key={benefit.id} className="flex items-start gap-6">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center mt-1 ${
                        benefit.is_achieved ? 'bg-primary' : 'bg-muted border border-border'
                      }`}>
                        <IconComponent className={`w-6 h-6 ${
                          benefit.is_achieved ? 'text-white' : 'text-muted-foreground'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-foreground mb-2">{benefit.name}</h3>
                        <p className="text-muted-foreground mb-3 leading-relaxed">
                          {benefit.description}
                        </p>
                        <div className={`text-sm font-medium ${
                          benefit.is_achieved ? 'text-primary' : 'text-muted-foreground'
                        }`}>
                          {benefit.is_achieved
                            ? '✓ Achieved'
                            : `Coming in ${benefit.days_required - Math.floor((Date.now() - new Date(userProgress.quit_date).getTime()) / (1000 * 60 * 60 * 24))} days`
                          }
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </section>
        )}

        {/* Apple Mac Desktop Motivation Section */}
        <section className="bg-card rounded-2xl p-16 text-center border border-border">
          <div className="w-20 h-20 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-6">
            <Award className="w-10 h-10 text-white" />
          </div>
          <h2 className="text-3xl font-royal text-foreground mb-6">
            Keep Going Strong on Your Journey!
          </h2>
          <p className="text-lg text-muted-foreground mb-10 max-w-3xl mx-auto leading-relaxed">
            You're making incredible progress in your smoking cessation journey. Every day smoke-free is a significant victory
            worth celebrating, and each milestone brings you closer to a healthier, smoke-free life.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={handleShareProgress}
              className="bg-primary text-white px-8 py-4 rounded-xl hover:bg-primary/90 font-medium transition-all"
            >
              Share Your Progress
            </button>
            <button
              onClick={handleViewReport}
              className="bg-background text-foreground px-8 py-4 rounded-xl hover:bg-muted font-medium transition-all border border-border"
            >
              View Detailed Report
            </button>
          </div>
        </section>
      </main>
    </div>
  );
};

export default ProgressPage;
