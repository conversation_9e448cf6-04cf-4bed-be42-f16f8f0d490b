import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Users, MessageCircle, TrendingUp, Award, ThumbsUp, Calendar } from 'lucide-react';
import { getCommunityPosts, CommunityPost, getCommunityStats, CommunityStats } from '../lib/supabase';

const CommunityPage: React.FC = () => {
  const [posts, setPosts] = useState<CommunityPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // HOLY RULE 1 COMPLIANCE: Real database stats instead of hardcoded
  const [communityStats, setCommunityStats] = useState<CommunityStats>({
    expertReviews: 0,
    activeMembers: 0,
    successRating: 0,
    productsReviewed: 0
  });

  useEffect(() => {
    const loadCommunityData = async () => {
      try {
        setLoading(true);

        // Load community posts
        const postsData = await getCommunityPosts();
        setPosts(postsData || []);

        // Load real community statistics from database
        const statsData = await getCommunityStats();
        setCommunityStats(statsData);

        setError(null);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to load community data');
      } finally {
        setLoading(false);
      }
    };
    loadCommunityData();
  }, []);
  return (
    <div className="bg-background">
      {/* Apple Mac Desktop Premium Header */}
      <header className="bg-background/99 backdrop-blur-2xl border-b border-border/25 shadow-[0_2px_8px_rgba(0,0,0,0.02)]">
        <div className="max-w-7xl mx-auto px-8 py-16">
          <div className="text-center">
            <div className="inline-flex items-center gap-4 bg-primary/8 backdrop-blur-xl px-8 py-4 rounded-2xl mb-8 border border-primary/10">
              <Users className="w-6 h-6 text-primary" />
              <span className="text-primary font-refined font-semibold text-lg">Community Hub</span>
            </div>
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-royal text-foreground mb-8 tracking-royal leading-[1.02]">
              NRT Community & Support
            </h1>
            <p className="text-xl sm:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-sophisticated">
              Connect with thousands of people on their quit journey. Share experiences, get expert advice,
              read authentic product reviews, and find the support you need to succeed in your smoking cessation goals.
            </p>
          </div>
        </div>
      </header>

      {/* Apple Mac Desktop Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Apple Mac Desktop Premium Community Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
          <div className="bg-background/90 backdrop-blur-2xl rounded-[2rem] p-10 border border-border/20 text-center hover:border-border/40 hover:shadow-[0_12px_32px_rgba(0,0,0,0.06)] transition-all duration-400 group hover:scale-[1.02]">
            <div className="w-20 h-20 bg-primary/10 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-[0_6px_20px_rgba(34,197,94,0.08)] border border-primary/15 group-hover:scale-105 transition-transform duration-400">
              <Award className="w-10 h-10 text-primary" />
            </div>
            <div className="text-4xl font-royal text-foreground mb-3 tracking-royal">{communityStats.expertReviews.toLocaleString()}</div>
            <div className="text-base text-muted-foreground font-refined">Expert Reviews & Ratings</div>
          </div>

          <div className="bg-background/90 backdrop-blur-2xl rounded-[2rem] p-10 border border-border/20 text-center hover:border-border/40 hover:shadow-[0_12px_32px_rgba(0,0,0,0.06)] transition-all duration-400 group hover:scale-[1.02]">
            <div className="w-20 h-20 bg-primary/10 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-[0_6px_20px_rgba(34,197,94,0.08)] border border-primary/15 group-hover:scale-105 transition-transform duration-400">
              <Users className="w-10 h-10 text-primary" />
            </div>
            <div className="text-4xl font-royal text-foreground mb-3 tracking-royal">{communityStats.activeMembers.toLocaleString()}</div>
            <div className="text-base text-muted-foreground font-refined">Active Community Members</div>
          </div>

          <div className="bg-background/90 backdrop-blur-2xl rounded-[2rem] p-10 border border-border/20 text-center hover:border-border/40 hover:shadow-[0_12px_32px_rgba(0,0,0,0.06)] transition-all duration-400 group hover:scale-[1.02]">
            <div className="w-20 h-20 bg-primary/10 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-[0_6px_20px_rgba(34,197,94,0.08)] border border-primary/15 group-hover:scale-105 transition-transform duration-400">
              <ThumbsUp className="w-10 h-10 text-primary" />
            </div>
            <div className="text-4xl font-royal text-foreground mb-3 tracking-royal">{communityStats.successRating}</div>
            <div className="text-base text-muted-foreground font-refined">Average Success Rating</div>
          </div>

          <div className="bg-background/90 backdrop-blur-2xl rounded-[2rem] p-10 border border-border/20 text-center hover:border-border/40 hover:shadow-[0_12px_32px_rgba(0,0,0,0.06)] transition-all duration-400 group hover:scale-[1.02]">
            <div className="w-20 h-20 bg-primary/10 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-[0_6px_20px_rgba(34,197,94,0.08)] border border-primary/15 group-hover:scale-105 transition-transform duration-400">
              <TrendingUp className="w-10 h-10 text-primary" />
            </div>
            <div className="text-4xl font-royal text-foreground mb-3 tracking-royal">{communityStats.productsReviewed.toLocaleString()}</div>
            <div className="text-base text-muted-foreground font-refined">NRT Products Reviewed</div>
          </div>
        </div>

        {/* Apple Mac Desktop Recent Discussions */}
        <section className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-lg font-refined text-foreground mb-1 tracking-refined">Recent Community Discussions</h2>
              <p className="text-muted-foreground text-sm">Join conversations about NRT products, quit strategies, and success stories</p>
            </div>
            <Link
              to="/community?action=create"
              className="bg-primary text-background px-4 py-2 rounded-lg hover:bg-primary/90 font-refined transition-all flex items-center gap-2 text-sm"
            >
              <MessageCircle className="w-3 h-3" />
              Start New Discussion
            </Link>
          </div>

          {/* Apple Mac Desktop Loading State */}
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="w-6 h-6 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
              <span className="ml-3 text-muted-foreground text-sm">Loading community discussions and member posts...</span>
            </div>
          )}

          {/* Apple Mac Desktop Error State */}
          {error && (
            <div className="bg-destructive/10 border border-destructive/20 rounded-2xl p-6 mb-8">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-destructive rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">!</span>
                </div>
                <div>
                  <p className="text-destructive font-medium">Unable to load community discussions</p>
                  <p className="text-muted-foreground text-sm mt-1">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Apple Mac Desktop Community Posts Data */}
          {!loading && !error && (
            <div className="space-y-6">
              {posts.length > 0 ? (
                posts.map((post) => (
                  <div key={post.id} className="bg-card rounded-2xl p-6 shadow-sm border border-border hover:shadow-md transition-all">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-white font-bold text-sm">
                          {post.user_profile?.full_name ?
                            post.user_profile.full_name.split(' ').map(n => n[0]).join('').slice(0, 2).toUpperCase() :
                            '??'
                          }
                        </span>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <h3 className="font-semibold text-foreground">{post.title}</h3>
                          <span className="text-sm text-muted-foreground">
                            {(() => {
                              const now = new Date();
                              const postDate = new Date(post.created_at);
                              const diffMs = now.getTime() - postDate.getTime();
                              const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

                              if (diffHours < 1) return 'Just now';
                              if (diffHours === 1) return '1 hour ago';
                              if (diffHours < 24) return `${diffHours} hours ago`;

                              const diffDays = Math.floor(diffHours / 24);
                              if (diffDays === 1) return '1 day ago';
                              return `${diffDays} days ago`;
                            })()}
                          </span>
                        </div>
                        <p className="text-muted-foreground mb-4 leading-relaxed">{post.content}</p>
                        <div className="flex items-center gap-6 text-sm">
                          <button
                            className="flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors cursor-pointer"
                            onClick={() => {
                              // Handle like functionality
                              console.log('Liked post:', post.id);
                            }}
                          >
                            <ThumbsUp className="w-4 h-4" />
                            <span>{post.likes_count || 0} helpful votes</span>
                          </button>
                          <button
                            className="flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors cursor-pointer"
                            onClick={() => {
                              // Handle reply functionality
                              console.log('Reply to post:', post.id);
                            }}
                          >
                            <MessageCircle className="w-4 h-4" />
                            <span>{post.replies_count || 0} community replies</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="bg-card rounded-2xl p-8 shadow-sm border border-border text-center">
                  <div className="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="font-semibold text-foreground mb-2">Welcome to the NRT Community</h3>
                  <p className="text-muted-foreground">No community discussions yet. Be the first to share your quit journey experience, ask questions, or offer support to fellow members!</p>
                </div>
              )}
            </div>
          )}
        </section>

        {/* Apple Mac Desktop Community Features */}
        <section className="mb-8">
          <div className="text-center mb-6">
            <h2 className="text-lg font-refined text-foreground mb-2 tracking-refined">Comprehensive Community Support</h2>
            <p className="text-sm text-muted-foreground max-w-3xl mx-auto">
              Access multiple support channels designed to help you succeed in your quit journey with peer support,
              expert guidance, and proven cessation strategies.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="bg-background rounded-lg p-4 border border-muted text-center">
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mx-auto mb-3">
                <MessageCircle className="w-6 h-6 text-background" />
              </div>
              <h3 className="text-sm font-refined text-foreground mb-2">Expert Discussion Forums</h3>
              <p className="text-muted-foreground mb-3 leading-relaxed text-xs">
                Join topic-specific discussions about NRT products, quit strategies, withdrawal management,
                and success stories with verified community members and cessation experts.
              </p>
              <Link to="/community?tab=discussions" className="inline-block bg-primary text-background px-3 py-2 rounded-lg hover:bg-primary/90 font-refined transition-all text-xs">
                Browse Active Discussions
              </Link>
            </div>

            <div className="bg-background rounded-lg p-4 border border-muted text-center">
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mx-auto mb-3">
                <Award className="w-6 h-6 text-background" />
              </div>
              <h3 className="text-sm font-refined text-foreground mb-2">Progress Achievement System</h3>
              <p className="text-muted-foreground mb-3 leading-relaxed text-xs">
                Track your quit journey milestones, earn achievement badges, monitor health improvements,
                and celebrate success markers with detailed progress analytics and community recognition.
              </p>
              <Link to="/progress" className="inline-block bg-primary text-background px-3 py-2 rounded-lg hover:bg-primary/90 font-refined transition-all text-xs">
                View Your Progress
              </Link>
            </div>

            <div className="bg-background rounded-lg p-4 border border-muted text-center">
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mx-auto mb-3">
                <Calendar className="w-6 h-6 text-background" />
              </div>
              <h3 className="text-sm font-refined text-foreground mb-2">Scheduled Support Groups</h3>
              <p className="text-muted-foreground mb-3 leading-relaxed text-xs">
                Join live group sessions with peers at similar quit stages, participate in guided discussions
                with certified cessation counselors, and access 24/7 crisis support when needed.
              </p>
              <Link to="/community?tab=groups" className="inline-block bg-primary text-background px-3 py-2 rounded-lg hover:bg-primary/90 font-refined transition-all text-xs">
                Find Support Groups
              </Link>
            </div>
          </div>
        </section>

        {/* Apple Mac Desktop Call to Action */}
        <section className="bg-card rounded-2xl p-12 text-center border border-border">
          <div className="w-20 h-20 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-6">
            <Users className="w-10 h-10 text-white" />
          </div>
          <h2 className="text-3xl font-royal text-foreground mb-4">
            Ready to Join Our Supportive Community?
          </h2>
          <p className="text-lg text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
            Connect with thousands of people who understand your quit journey challenges and victories.
            Get personalized support, share experiences, access expert advice, and celebrate milestones together
            in a judgment-free environment designed for your success.
          </p>
          <Link to="/community?action=signup" className="inline-block bg-primary text-white px-12 py-4 rounded-2xl hover:bg-primary/90 font-medium transition-all text-lg">
            Join Community & Start Your Journey
          </Link>
        </section>
      </main>
    </div>
  );
};

export default CommunityPage;
