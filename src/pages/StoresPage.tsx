import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { MapPin, Phone, Star, Shield, Package, Users, Award, Search, Grid, List, Filter, Sparkles } from 'lucide-react';

// Use mission_fresh schema for ALL data - USER REQUIREMENT
import { supabase } from '../lib/supabase';

interface Store {
  id: string;
  name: string;
  brand: string;
  chain: string | null;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  latitude: number;
  longitude: number;
  phone: string;
  website: string | null;
  store_hours: any;
  nrt_brands_carried: string[];
  pharmacy_available: boolean;
  prescription_required: boolean;
  drive_through: boolean;
  parking_available: boolean;
  wheelchair_accessible: boolean;
  rating: number | null;
  review_count: number | null;
  verified: boolean;
  created_at: string;
  updated_at: string;
}

const StoresPage: React.FC = () => {
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('distance'); // DEFAULT TO DISTANCE FOR CRAVING ACCESS
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    storeType: 'all',
    minRating: 0,
    verified: false,
    state: 'all',
    features: [] as string[]
  });
  
  // 🚨 CRITICAL BUSINESS LOGIC: "CRAVING HIT? FIND NEAREST STORE!"
  const [userLocation, setUserLocation] = useState<{lat: number, lng: number} | null>(null);
  const [locationLoading, setLocationLoading] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [nearestStores, setNearestStores] = useState<Store[]>([]);
  const [showEmergencyFinder, setShowEmergencyFinder] = useState(false);

  // 🚨 CRITICAL: Calculate distance between two coordinates (Haversine formula)
  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const R = 3959; // Earth's radius in miles
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c; // Distance in miles
  };

  // 🚨 EMERGENCY STORE FINDER: "CRAVING HIT? FIND NEAREST STORE NOW!"
  const findNearestStores = async () => {
    if (!navigator.geolocation) {
      setLocationError('Geolocation not supported by this browser');
      return;
    }

    setLocationLoading(true);
    setLocationError(null);

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const userLat = position.coords.latitude;
        const userLng = position.coords.longitude;
        setUserLocation({ lat: userLat, lng: userLng });

        // Calculate distances and find nearest stores
        const storesWithDistance = stores.map(store => ({
          ...store,
          distance: calculateDistance(userLat, userLng, store.latitude, store.longitude)
        })).sort((a, b) => a.distance - b.distance);

        setNearestStores(storesWithDistance.slice(0, 5)); // Top 5 nearest stores
        setShowEmergencyFinder(true);
        setLocationLoading(false);
      },
      (error) => {
        setLocationError('Unable to get your location. Please enable location services.');
        setLocationLoading(false);
        console.error('Geolocation error:', error);
      },
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 300000 }
    );
  };

  useEffect(() => {
    const loadStores = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const { data: storesData, error: storesError } = await supabase
          .from('stores')
          .select('*')
          .order('rating', { ascending: false });

        if (storesError) {
          console.error('Store loading error:', storesError);
          setError('Unable to connect to store database. Please try again later.');
          return;
        }

        setStores(storesData || []);
      } catch (error) {
        console.error('Store loading error:', error);
        setError('Store directory temporarily unavailable. Please check back soon.');
      } finally {
        setLoading(false);
      }
    };
    
    loadStores();
  }, []);

  // Filter and sort stores
  const filteredStores = useMemo(() => {
    console.log('Filtering stores:', stores.length, 'stores loaded');
    console.log('Search query:', searchQuery);
    console.log('Filters:', filters);
    
    let filtered = stores.filter(store => {
      const matchesSearch = searchQuery === '' || 
                           store.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           store.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           store.state.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesType = filters.storeType === 'all' || store.brand === filters.storeType;
      const matchesRating = (store.rating || 0) >= filters.minRating;
      const matchesVerified = !filters.verified || store.verified;
      const matchesState = filters.state === 'all' || store.state === filters.state;
      // Fix array handling for nrt_brands_carried
      const matchesFeatures = filters.features.length === 0 || 
                              (Array.isArray(store.nrt_brands_carried) && 
                               filters.features.every(feature => store.nrt_brands_carried.includes(feature)));
      
      const matches = matchesSearch && matchesType && matchesRating && matchesVerified && matchesState && matchesFeatures;
      if (!matches && stores.length < 10) {
        console.log('Store filtered out:', store.name, {
          matchesSearch, matchesType, matchesRating, matchesVerified, matchesState, matchesFeatures
        });
      }
      return matches;
    });

    // Sort stores - INCLUDING DISTANCE FOR CRAVING ACCESS
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'distance':
          if (userLocation) {
            const distA = calculateDistance(userLocation.lat, userLocation.lng, a.latitude, a.longitude);
            const distB = calculateDistance(userLocation.lat, userLocation.lng, b.latitude, b.longitude);
            return distA - distB;
          }
          return (b.rating || 0) - (a.rating || 0); // Fallback to rating if no location
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'name':
          return a.name.localeCompare(b.name);
        case 'city':
          return a.city.localeCompare(b.city);
        case 'reviews':
          return (b.review_count || 0) - (a.review_count || 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [stores, searchQuery, filters, sortBy]);

  // Overall statistics
  const overallStats = useMemo(() => {
    const states = [...new Set(stores.map(s => s.state))];
    const storeTypes = [...new Set(stores.map(s => s.brand))];
    const avgRating = stores.length > 0 
      ? stores.reduce((sum, store) => sum + (store.rating || 0), 0) / stores.length 
      : 0;
    const totalReviews = stores.reduce((sum, store) => sum + (store.review_count || 0), 0);
    const verifiedCount = stores.filter(store => store.verified).length;
    
    return {
      totalStores: stores.length,
      states: states.length,
      avgRating,
      totalReviews,
      verifiedCount,
      stateList: states,
      storeTypeList: storeTypes
    };
  }, [stores]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-20 h-20 bg-primary rounded-3xl flex items-center justify-center animate-pulse mx-auto mb-6">
            <Package className="w-10 h-10 text-white" />
          </div>
          <p className="text-primary font-medium text-lg">Loading store directory...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-20 h-20 bg-destructive rounded-3xl flex items-center justify-center mx-auto mb-6">
            <Package className="w-10 h-10 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-foreground mb-4">Error Loading Stores</h2>
          <p className="text-muted-foreground mb-6">{error}</p>
          <Link
            to="/store-locator"
            className="bg-primary text-primary-foreground px-6 py-3 rounded-2xl hover:bg-primary/90 transition-colors font-medium"
          >
            Try Store Locator Instead
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Enhanced Apple-style Main Navigation with Reasonable Font Sizes */}
      <nav className="sticky top-0 z-50 bg-background/98 backdrop-blur-md border-b border-border/30 shadow-[0_1px_3px_rgba(0,0,0,0.04)]">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">

        </div>
      </nav>

      {/* Breadcrumb Navigation */}
      <div className="bg-background border-b border-primary">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-primary hover:text-primary/80 transition-colors font-medium">
              Home
            </Link>
            <span className="text-muted-foreground">/</span>
            <span className="text-foreground font-medium">Stores</span>
          </div>
        </div>
      </div>

      {/* Apple Mac Desktop Premium Hero Section */}
      <div className="bg-gradient-to-br from-background/99 via-background/95 to-background/90 backdrop-blur-2xl">
        <div className="max-w-7xl mx-auto px-8 py-20">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-4 bg-primary/8 backdrop-blur-xl px-8 py-4 rounded-2xl mb-8 border border-primary/10">
              <Sparkles className="w-6 h-6 text-primary" />
              <span className="text-primary font-refined font-semibold text-lg">Store Directory</span>
            </div>
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-royal text-foreground mb-8 tracking-royal leading-[1.02]">
              NRT Stores
            </h1>
            <p className="text-xl sm:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-sophisticated">
              Find local stores carrying nicotine replacement therapy products.
              Compare locations, hours, and inventory from trusted retailers nationwide.
            </p>
            
            {/* 🚨 Apple Mac Desktop Emergency Store Finder - Premium Design */}
            <div className="mt-12">
              <button
                onClick={findNearestStores}
                disabled={locationLoading}
                className="inline-flex items-center gap-4 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-12 py-6 rounded-3xl font-royal font-bold text-xl shadow-[0_8px_24px_rgba(220,38,38,0.25)] hover:shadow-[0_12px_32px_rgba(220,38,38,0.35)] transition-all duration-400 transform hover:scale-[1.02] active:scale-[0.98]"
              >
                <MapPin className="w-7 h-7" />
                {locationLoading ? 'Finding Stores...' : 'CRAVING HIT? FIND NEAREST STORE!'}
              </button>
              {locationError && (
                <p className="text-red-600 mt-4 text-base font-refined font-medium">{locationError}</p>
              )}
            </div>
          </div>

          {/* Apple Mac Desktop Premium Statistics Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-6xl mx-auto">
            <div className="bg-background/90 backdrop-blur-2xl rounded-[2rem] p-10 shadow-[0_8px_24px_rgba(0,0,0,0.04)] border border-primary/20 text-center hover:shadow-[0_12px_32px_rgba(0,0,0,0.06)] transition-all duration-400 hover:scale-[1.02]">
              <div className="w-20 h-20 bg-primary/10 rounded-3xl flex items-center justify-center mx-auto mb-6">
                <Package className="w-10 h-10 text-primary" />
              </div>
              <div className="text-4xl font-royal font-bold text-primary mb-3">
                {overallStats.totalStores}
              </div>
              <div className="text-primary font-refined font-medium text-lg">Total Stores</div>
            </div>

            <div className="bg-background/90 backdrop-blur-2xl rounded-[2rem] p-10 shadow-[0_8px_24px_rgba(0,0,0,0.04)] border border-primary/20 text-center hover:shadow-[0_12px_32px_rgba(0,0,0,0.06)] transition-all duration-400 hover:scale-[1.02]">
              <div className="w-20 h-20 bg-primary/10 rounded-3xl flex items-center justify-center mx-auto mb-6">
                <Star className="w-10 h-10 text-primary" />
              </div>
              <div className="text-4xl font-royal font-bold text-primary mb-3">
                {overallStats.avgRating.toFixed(1)}
              </div>
              <div className="text-primary font-refined font-medium text-lg">Average Rating</div>
            </div>

            <div className="bg-background/90 backdrop-blur-2xl rounded-[2rem] p-10 shadow-[0_8px_24px_rgba(0,0,0,0.04)] border border-primary/20 text-center hover:shadow-[0_12px_32px_rgba(0,0,0,0.06)] transition-all duration-400 hover:scale-[1.02]">
              <div className="w-20 h-20 bg-primary/10 rounded-3xl flex items-center justify-center mx-auto mb-6">
                <Users className="w-10 h-10 text-primary" />
              </div>
              <div className="text-4xl font-royal font-bold text-primary mb-3">
                {overallStats.totalReviews}
              </div>
              <div className="text-primary font-refined font-medium text-lg">Total Reviews</div>
            </div>

            <div className="bg-background/90 backdrop-blur-2xl rounded-[2rem] p-10 shadow-[0_8px_24px_rgba(0,0,0,0.04)] border border-primary/20 text-center hover:shadow-[0_12px_32px_rgba(0,0,0,0.06)] transition-all duration-400 hover:scale-[1.02]">
              <div className="w-20 h-20 bg-primary/10 rounded-3xl flex items-center justify-center mx-auto mb-6">
                <Award className="w-10 h-10 text-primary" />
              </div>
              <div className="text-4xl font-royal font-bold text-primary mb-3">
                {overallStats.verifiedCount}
              </div>
              <div className="text-primary font-refined font-medium text-lg">Verified Stores</div>
            </div>
          </div>
        </div>
      </div>

      {/* 🚨 EMERGENCY NEAREST STORES RESULTS - CRITICAL BUSINESS FEATURE */}
      {showEmergencyFinder && nearestStores.length > 0 && (
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="bg-red-50 border border-red-200 rounded-3xl p-8 mb-8">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-red-800 mb-2">
                🚨 NEAREST NRT STORES FOR IMMEDIATE ACCESS
              </h2>
              <p className="text-red-700 font-medium">
                Found {nearestStores.length} stores near your location
              </p>
            </div>
            
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {nearestStores.map((store, index) => {
                const distance = userLocation ? 
                  calculateDistance(userLocation.lat, userLocation.lng, store.latitude, store.longitude).toFixed(1) : 
                  'N/A';
                return (
                  <div key={store.id} className="bg-white rounded-2xl p-6 shadow-lg border border-red-200">
                    <div className="flex items-center justify-between mb-3">
                      <span className="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-bold">
                        #{index + 1} NEAREST
                      </span>
                      <span className="text-red-600 font-bold text-lg">
                        {distance} mi
                      </span>
                    </div>
                    <h3 className="font-bold text-foreground text-lg mb-2">{store.name}</h3>
                    <p className="text-muted-foreground text-sm mb-2">
                      {store.address}, {store.city}, {store.state}
                    </p>
                    <div className="flex items-center gap-4 text-sm">
                      {store.rating && (
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                          <span className="font-medium">{store.rating.toFixed(1)}</span>
                        </div>
                      )}
                      {store.verified && (
                        <div className="flex items-center gap-1 text-primary">
                          <Shield className="w-4 h-4" />
                          <span className="text-sm font-medium">Verified</span>
                        </div>
                      )}
                      {store.phone && (
                        <div className="flex items-center gap-1 text-muted-foreground">
                          <Phone className="w-4 h-4" />
                          <span className="text-sm">{store.phone}</span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
            
            <div className="text-center mt-6">
              <button
                onClick={() => setShowEmergencyFinder(false)}
                className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-xl font-medium transition-colors"
              >
                Close Emergency Finder
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filter Controls */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="bg-background rounded-3xl shadow-xl border border-border/50 p-8 mb-8">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Search Bar */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-primary w-5 h-5" />
                <input
                  type="text"
                  name="storeSearch"
                  placeholder="Search stores by name, city, or state..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 border border-border rounded-2xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg"
                />
              </div>
            </div>

            {/* Sort and View Controls */}
            <div className="flex items-center gap-4">
              <select
                name="sortBy"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-4 border border-border rounded-2xl focus:ring-2 focus:ring-primary focus:border-transparent bg-background"
              >
                <option value="rating">Sort by Rating</option>
                <option value="name">Sort by Name</option>
                <option value="city">Sort by City</option>
                <option value="reviews">Sort by Reviews</option>
              </select>

              <div className="flex bg-muted rounded-2xl p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-3 rounded-xl transition-colors ${
                    viewMode === 'grid' ? 'bg-background shadow-sm text-primary' : 'text-foreground'
                  }`}
                >
                  <Grid className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-3 rounded-xl transition-colors ${
                    viewMode === 'list' ? 'bg-background shadow-sm text-primary' : 'text-foreground'
                  }`}
                >
                  <List className="w-5 h-5" />
                </button>
              </div>

              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`px-6 py-4 rounded-2xl font-medium transition-colors ${
                  showFilters ? 'bg-primary text-primary-foreground' : 'bg-background text-foreground hover:bg-muted'
                }`}
              >
                <Filter className="w-5 h-5 inline mr-2" />
                Filters
              </button>
            </div>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="mt-8 pt-8 border-t border-border">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">Store Type</label>
                  <select
                    name="storeType"
                    value={filters.storeType}
                    onChange={(e) => setFilters({...filters, storeType: e.target.value})}
                    className="w-full px-3 py-2 border border-border rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="all">All Types</option>
                    {overallStats.storeTypeList.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">State</label>
                  <select
                    name="state"
                    value={filters.state}
                    onChange={(e) => setFilters({...filters, state: e.target.value})}
                    className="w-full px-3 py-2 border border-border rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="all">All States</option>
                    {overallStats.stateList.map(state => (
                      <option key={state} value={state}>{state}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">Minimum Rating</label>
                  <select
                    name="minRating"
                    value={filters.minRating}
                    onChange={(e) => setFilters({...filters, minRating: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-border rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value={0}>Any Rating</option>
                    <option value={3}>3+ Stars</option>
                    <option value={4}>4+ Stars</option>
                    <option value={4.5}>4.5+ Stars</option>
                  </select>
                </div>

                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="verified"
                      checked={filters.verified}
                      onChange={(e) => setFilters({...filters, verified: e.target.checked})}
                      className="rounded border-border text-primary focus:ring-primary"
                    />
                    <span className="ml-2 text-sm font-medium text-foreground">Verified Only</span>
                  </label>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Results Count */}
        <div className="flex items-center justify-between mb-8">
          <p className="text-foreground">
            Showing {filteredStores.length} of {stores.length} stores
          </p>
        </div>

        {/* Store Grid/List */}
        {filteredStores.length > 0 ? (
          <div className={viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
            : 'space-y-6'
          }>
            {filteredStores.map(store => (
              <Link
                key={store.id}
                to={`/store/${store.id}`}
                className={`group bg-background rounded-3xl shadow-lg border border-border/50 hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 overflow-hidden ${
                  viewMode === 'list' ? 'flex items-center p-8' : 'p-8'
                }`}
              >
                {viewMode === 'grid' ? (
                  <div>
                    <div className="flex items-start justify-between mb-6">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-bold text-foreground text-xl group-hover:text-primary transition-colors">
                            {store.name}
                          </h3>
                          {store.verified && (
                            <Shield className="w-5 h-5 text-primary" />
                          )}
                        </div>
                        <p className="text-muted-foreground mb-2">{store.brand}</p>
                        {store.chain && (
                          <span className="inline-block bg-muted text-foreground px-3 py-1 rounded-full text-sm font-medium">
                            {store.chain}
                          </span>
                        )}
                      </div>
                      {store.rating && (
                        <div className="text-right">
                          <div className="flex items-center gap-1 mb-1">
                            <Star className="w-4 h-4 text-rating-gold fill-current" />
                            <span className="font-bold text-foreground">{store.rating.toFixed(1)}</span>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {store.review_count} reviews
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="space-y-3 mb-6">
                      <div className="flex items-center gap-3 text-muted-foreground">
                        <MapPin className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">
                          {store.address}, {store.city}, {store.state} {store.zip_code}
                        </span>
                      </div>
                      {store.phone && (
                        <div className="flex items-center gap-3 text-muted-foreground">
                          <Phone className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{store.phone}</span>
                        </div>
                      )}
                    </div>

                    {store.nrt_brands_carried && store.nrt_brands_carried.length > 0 && (
                      <div className="space-y-3">
                        <div className="flex flex-wrap gap-2">
                          {store.nrt_brands_carried.slice(0, 3).map(brand => (
                            <span
                              key={brand}
                              className="bg-muted text-foreground px-3 py-1 rounded-full text-xs font-medium"
                            >
                              {brand}
                            </span>
                          ))}
                          {store.nrt_brands_carried.length > 3 && (
                            <span className="text-xs text-muted-foreground">
                              +{store.nrt_brands_carried.length - 3} more
                            </span>
                          )}
                        </div>

                        {/* Legal Compliance Indicator */}
                        <div className="flex items-center gap-2 px-3 py-2 bg-green-50 border border-green-200 rounded-lg">
                          <Shield className="w-4 h-4 text-green-600" />
                          <span className="text-sm font-medium text-green-800">Carries FDA-Approved NRT Products</span>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center gap-6 w-full">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-bold text-foreground text-xl group-hover:text-primary transition-colors">
                          {store.name}
                        </h3>
                        {store.verified && (
                          <Shield className="w-5 h-5 text-primary" />
                        )}
                      </div>
                      <p className="text-muted-foreground mb-2">
                        {store.address}, {store.city}, {store.state} {store.zip_code}
                      </p>

                      {/* NRT Brands and Legal Compliance for List View */}
                      {store.nrt_brands_carried && store.nrt_brands_carried.length > 0 && (
                        <div className="space-y-2 mb-2">
                          <div className="flex flex-wrap gap-1">
                            {store.nrt_brands_carried.slice(0, 2).map(brand => (
                              <span
                                key={brand}
                                className="bg-muted text-foreground px-2 py-1 rounded text-xs font-medium"
                              >
                                {brand}
                              </span>
                            ))}
                            {store.nrt_brands_carried.length > 2 && (
                              <span className="text-xs text-muted-foreground">
                                +{store.nrt_brands_carried.length - 2} more
                              </span>
                            )}
                          </div>

                          <div className="flex items-center gap-2 px-2 py-1 bg-green-50 border border-green-200 rounded text-xs">
                            <Shield className="w-3 h-3 text-green-600" />
                            <span className="font-medium text-green-800">FDA-Approved NRT Available</span>
                          </div>
                        </div>
                      )}
                    </div>
                    {store.rating && (
                      <div className="text-right">
                        <div className="flex items-center gap-1 mb-1">
                          <Star className="w-4 h-4 text-rating-gold fill-current" />
                          <span className="font-bold text-foreground">{store.rating.toFixed(1)}</span>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {store.review_count} reviews
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="w-20 h-20 bg-white rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Package className="w-10 h-10 text-primary" />
            </div>
            <h3 className="text-xl font-bold text-foreground mb-4">No Stores Found</h3>
            <p className="text-muted-foreground mb-6">
              Try adjusting your search criteria or filters to find more stores.
            </p>
            <button
              onClick={() => {
                setSearchQuery('');
                setFilters({
                  storeType: 'all',
                  minRating: 0,
                  verified: false,
                  state: 'all',
                  features: []
                });
              }}
              className="bg-primary text-primary-foreground px-6 py-3 rounded-2xl hover:bg-primary/90 transition-colors font-medium"
            >
              Clear All Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default StoresPage;
