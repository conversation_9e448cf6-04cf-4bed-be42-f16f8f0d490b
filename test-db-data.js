#!/usr/bin/env node

// Quick test to verify database connectivity and data availability
// HOLY RULE #1 COMPLIANCE: Testing real database data only

import { createClient } from '@supabase/supabase-js';

// Direct Supabase configuration (same as app)
const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc';

const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  db: { schema: 'mission_fresh' }
});

async function testDatabaseData() {
  console.log('🚨 TESTING DATABASE DATA AVAILABILITY...');
  console.log('🚨 HOLY RULE #1 COMPLIANCE: Checking real database data only');
  
  try {
    // Test 1: Check testimonials table
    console.log('\n📊 TEST 1: Checking testimonials table...');
    const { data: testimonialsData, error: testimonialsError } = await supabase
      .from('testimonials')
      .select('id, user_name, content, rating')
      .limit(5);

    if (testimonialsError) {
      console.error('❌ Testimonials table error:', testimonialsError);
    } else {
      console.log('✅ Testimonials table accessible');
      console.log(`📈 Found ${testimonialsData?.length || 0} testimonials in testimonials table`);
      if (testimonialsData && testimonialsData.length > 0) {
        console.log('📝 Sample testimonial:', testimonialsData[0].user_name, '-', testimonialsData[0].content?.substring(0, 50) + '...');
      }
    }

    // Test 2: Check smokeless_products table
    console.log('\n📊 TEST 2: Checking smokeless_products table...');
    const { data: productsData, error: productsError } = await supabase
      .from('smokeless_products')
      .select('id, name, brand, category')
      .limit(5);
    
    if (productsError) {
      console.error('❌ Smokeless products table error:', productsError);
    } else {
      console.log('✅ Smokeless products table accessible');
      console.log(`📈 Found ${productsData?.length || 0} products in smokeless_products table`);
      if (productsData && productsData.length > 0) {
        console.log('📝 Sample product:', productsData[0].name, 'by', productsData[0].brand);
      }
    }

    // Test 3: Check vendor_prices table
    console.log('\n📊 TEST 3: Checking vendor_prices table...');
    const { data: vendorPricesData, error: vendorPricesError } = await supabase
      .from('vendor_prices')
      .select('id, vendor_name, price')
      .limit(3);

    if (vendorPricesError) {
      console.error('❌ Vendor prices table error:', vendorPricesError);
    } else {
      console.log('✅ Vendor prices table accessible');
      console.log(`📈 Found ${vendorPricesData?.length || 0} vendor prices in vendor_prices table`);
    }

    // Test 4: Check deals table
    console.log('\n📊 TEST 4: Checking deals table...');
    const { data: dealsData, error: dealsError } = await supabase
      .from('deals')
      .select('id, title, vendor_name')
      .limit(3);

    if (dealsError) {
      console.error('❌ Deals table error:', dealsError);
    } else {
      console.log('✅ Deals table accessible');
      console.log(`📈 Found ${dealsData?.length || 0} deals in deals table`);
    }

    // Test 5: Test authentication with real user account
    console.log('\n📊 TEST 5: Testing authentication with real user account...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'J4913836j'
    });

    if (authError) {
      console.error('❌ Authentication error:', authError.message);
    } else {
      console.log('✅ Authentication successful');
      console.log('👤 User ID:', authData.user?.id);
      console.log('📧 User email:', authData.user?.email);

      // Sign out after test
      await supabase.auth.signOut();
      console.log('🚪 Signed out after test');
    }

    console.log('\n🎯 DATABASE DATA TEST COMPLETE');
    console.log('🚨 HOLY RULE #1 STATUS: All data queries target real database tables');
    
  } catch (error) {
    console.error('💥 CRITICAL ERROR during database data test:', error);
    process.exit(1);
  }
}

// Run the test
testDatabaseData()
  .then(() => {
    console.log('✅ Database data test completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Database data test failed:', error.message);
    process.exit(1);
  });
