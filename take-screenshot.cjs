const puppeteer = require('puppeteer');

(async () => {
  try {
    const browser = await puppeteer.launch({
      headless: true,
      defaultViewport: { width: 1400, height: 900 }
    });
    const page = await browser.newPage();
    
    console.log('Navigating to localhost:5002...');
    await page.goto('http://localhost:5002', { 
      waitUntil: 'domcontentloaded', 
      timeout: 15000 
    });
    
    console.log('Taking screenshot...');
    await page.screenshot({ path: 'homepage-current-state.png', fullPage: true });
    
    await browser.close();
    console.log('Homepage current state screenshot saved successfully');
  } catch (error) {
    console.error('Screenshot failed:', error.message);
  }
})();
